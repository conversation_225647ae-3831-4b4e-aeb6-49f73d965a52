// Dynamic imports to avoid build-time issues
// import pdf from 'pdf-parse'
// import * as J<PERSON><PERSON><PERSON> from 'jszip'
// import { parseString } from 'xml2js'
// import readingTime from 'reading-time'
// import { readabilityScore } from 'readability-score'
// import { WordTokenizer, SentenceTokenizer } from 'natural'

export interface Chapter {
  title: string
  content: string
  wordCount: number
  pageNumber?: number
  startPage?: number
  endPage?: number
}

export interface EbookMetadata {
  title?: string
  author?: string
  description?: string
  language?: string
  publisher?: string
  publishedDate?: string
  isbn?: string
  subjects?: string[]
  rights?: string
  format?: string
}

export interface ProcessingResult {
  chapters: Chapter[]
  wordCount: number
  pageCount: number
  readingTimeMinutes: number
  readabilityScore: number
  suggestedTags: string[]
  metadata: EbookMetadata
  description?: string
  contentAnalysis?: {
    complexity: 'Simple' | 'Moderate' | 'Complex'
    sentiment: 'Positive' | 'Neutral' | 'Negative'
    pacing: 'Fast' | 'Moderate' | 'Slow'
    dialogueRatio: number
    descriptiveRatio: number
    actionRatio: number
  }
}

// Tokenizers will be initialized dynamically

/**
 * Main function to process ebook files (PDF or EPUB)
 */
export async function processEbook(fileUrl: string, fileType: 'pdf' | 'epub'): Promise<ProcessingResult> {
  console.log(`Processing ${fileType.toUpperCase()} file:`, fileUrl)

  try {
    // Fetch the file
    const response = await fetch(fileUrl)
    if (!response.ok) {
      throw new Error(`Failed to fetch file: ${response.statusText}`)
    }

    const buffer = await response.arrayBuffer()

    if (fileType === 'pdf') {
      return await processPDF(Buffer.from(buffer))
    } else {
      return await processEPUB(Buffer.from(buffer))
    }
  } catch (error) {
    console.error('Error processing ebook:', error)
    throw new Error(`Failed to process ${fileType}: ${error.message}`)
  }
}

/**
 * Process ebook from buffer (for API endpoints)
 */
export async function processEbookBuffer(buffer: Buffer, fileType: 'pdf' | 'epub'): Promise<ProcessingResult> {
  console.log(`Processing ${fileType.toUpperCase()} buffer`)

  try {
    if (fileType === 'pdf') {
      return await processPDF(buffer)
    } else {
      return await processEPUB(buffer)
    }
  } catch (error) {
    console.error('Error processing ebook buffer:', error)
    throw error
  }
}

/**
 * Process PDF files
 */
async function processPDF(buffer: Buffer): Promise<ProcessingResult> {
  try {
    // Dynamic import to avoid build-time issues
    const pdf = (await import('pdf-parse')).default
    const { WordTokenizer, SentenceTokenizer } = await import('natural')
    const readingTime = (await import('reading-time')).default
    const readabilityScoreModule = await import('readability-score')
    const readabilityScore = readabilityScoreModule.default || readabilityScoreModule.readabilityScore || readabilityScoreModule

    const wordTokenizer = new WordTokenizer()
    const sentenceTokenizer = new SentenceTokenizer(['Dr.', 'Mr.', 'Mrs.', 'Ms.', 'Prof.', 'Sr.', 'Jr.'])

    const data = await pdf(buffer, {
      // Enhanced options for better text extraction
      normalizeWhitespace: true,
      disableCombineTextItems: false
    })

    const fullText = data.text
    const pageCount = data.numpages
    
    // Extract chapters from PDF
    const chapters = extractChaptersFromText(fullText, wordTokenizer)

    // Calculate metrics
    const wordCount = wordTokenizer.tokenize(fullText)?.length || 0
    const readingStats = readingTime(fullText)
    let readabilityScoreValue = 0
    try {
      readabilityScoreValue = typeof readabilityScore === 'function' ? readabilityScore(fullText) : 0
    } catch (error) {
      console.warn('Failed to calculate readability score:', error)
      readabilityScoreValue = 0
    }
    const suggestedTags = extractKeywords(fullText, [], wordTokenizer, sentenceTokenizer)
    
    // Extract basic metadata (PDFs have limited metadata)
    const metadata: EbookMetadata = {
      title: data.info?.Title || undefined,
      author: data.info?.Author || undefined,
      description: data.info?.Subject || undefined
    }

    return {
      chapters,
      wordCount,
      pageCount: pageCount, // Use actual PDF page count, not estimated
      readingTimeMinutes: Math.ceil(readingStats.minutes),
      readabilityScore: readabilityScoreValue,
      suggestedTags,
      metadata,
      description: generateDescription(fullText, sentenceTokenizer),
      contentAnalysis: analyzeContentQuality(fullText, wordTokenizer, sentenceTokenizer)
    }
  } catch (error) {
    console.error('PDF processing error:', error)
    throw new Error(`PDF processing failed: ${error.message}`)
  }
}

/**
 * Process EPUB files
 */
async function processEPUB(buffer: Buffer): Promise<ProcessingResult> {
  try {
    // Dynamic imports to avoid build-time issues
    const JSZip = (await import('jszip')).default
    const { parseString } = await import('xml2js')
    const { WordTokenizer, SentenceTokenizer } = await import('natural')
    const readingTime = (await import('reading-time')).default
    const readabilityScoreModule = await import('readability-score')
    const readabilityScore = readabilityScoreModule.default || readabilityScoreModule.readabilityScore || readabilityScoreModule

    const wordTokenizer = new WordTokenizer()
    const sentenceTokenizer = new SentenceTokenizer(['Dr.', 'Mr.', 'Mrs.', 'Ms.', 'Prof.', 'Sr.', 'Jr.'])

    // Local XML parsing function
    const parseXmlAsync = (xml: string): Promise<any> => {
      return new Promise((resolve, reject) => {
        parseString(xml, (err, result) => {
          if (err) reject(err)
          else resolve(result)
        })
      })
    }

    // Parse EPUB as ZIP file
    const zip = await JSZip.loadAsync(buffer)

    // Extract metadata from OPF file
    let metadata: EbookMetadata = {
      title: 'Unknown Title',
      author: 'Unknown Author',
      description: '',
      language: 'en',
      publisher: '',
      publishedDate: '',
      isbn: '',
      subjects: [],
      rights: '',
      format: 'epub'
    }

    // Find and parse the OPF file for metadata
    const containerFile = zip.file('META-INF/container.xml')
    if (containerFile) {
      const containerXml = await containerFile.async('text')
      const containerData = await parseXmlAsync(containerXml)

      // Get the OPF file path
      const opfPath = containerData?.container?.rootfiles?.[0]?.rootfile?.[0]?.$?.['full-path']
      if (opfPath) {
        const opfFile = zip.file(opfPath)
        if (opfFile) {
          const opfXml = await opfFile.async('text')
          const opfData = await parseXmlAsync(opfXml)

          // Extract metadata from OPF
          const opfMetadata = opfData?.package?.metadata?.[0]
          if (opfMetadata) {
            metadata.title = extractTextFromXmlElement(opfMetadata['dc:title']) || metadata.title
            metadata.author = extractTextFromXmlElement(opfMetadata['dc:creator']) || metadata.author
            metadata.description = extractTextFromXmlElement(opfMetadata['dc:description']) || metadata.description
            metadata.language = extractTextFromXmlElement(opfMetadata['dc:language']) || metadata.language
            metadata.publisher = extractTextFromXmlElement(opfMetadata['dc:publisher']) || metadata.publisher
            metadata.publishedDate = extractTextFromXmlElement(opfMetadata['dc:date']) || metadata.publishedDate
            metadata.isbn = extractTextFromXmlElement(opfMetadata['dc:identifier']) || metadata.isbn

            const subjects = opfMetadata['dc:subject']
            if (subjects) {
              metadata.subjects = Array.isArray(subjects)
                ? subjects.map(s => extractTextFromXmlElement(s)).filter(Boolean)
                : [extractTextFromXmlElement(subjects)].filter(Boolean)
            }
          }
        }
      }
    }

    const chapters: Chapter[] = []
    let fullText = ''
    let chapterNumber = 1

    // Find HTML/XHTML files in the EPUB
    const htmlFiles = Object.keys(zip.files).filter(filename =>
      filename.match(/\.(x?html?)$/i) && !filename.startsWith('META-INF/')
    )

    console.log(`Found ${htmlFiles.length} HTML files in EPUB:`, htmlFiles)

    // Sort files properly by chapter number
    const sortedHtmlFiles = htmlFiles.sort((a, b) => {
      const aMatch = a.match(/chapter-(\d+)/i)
      const bMatch = b.match(/chapter-(\d+)/i)

      if (aMatch && bMatch) {
        return parseInt(aMatch[1]) - parseInt(bMatch[1])
      }

      // Put non-chapter files at the end
      if (aMatch && !bMatch) return -1
      if (!aMatch && bMatch) return 1

      return a.localeCompare(b)
    })

    console.log('Sorted HTML files for processing:', sortedHtmlFiles)

    // Process each HTML file as a potential chapter (in correct order)
    for (const filename of sortedHtmlFiles) {
      try {
        const file = zip.file(filename)
        if (file) {
          const htmlContent = await file.async('text')
          const cleanText = cleanHtmlText(htmlContent)

          console.log(`Processing ${filename}:`, {
            htmlLength: htmlContent.length,
            cleanTextLength: cleanText.length,
            cleanTextPreview: cleanText.substring(0, 200)
          })

          if (cleanText.trim().length > 100) { // Only include substantial content
            // Try to extract title from HTML or use filename
            const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i)
            const h1Match = htmlContent.match(/<h1[^>]*>([^<]+)<\/h1>/i)
            const h2Match = htmlContent.match(/<h2[^>]*>([^<]+)<\/h2>/i)
            const chapterTitle = titleMatch?.[1] || h1Match?.[1] || h2Match?.[1] || `Chapter ${chapterNumber}`

            const wordCount = wordTokenizer.tokenize(cleanText)?.length || 0

            const chapter = {
              title: chapterTitle.trim(),
              content: cleanText,
              wordCount: wordCount
            }

            console.log(`Created chapter ${chapterNumber}:`, {
              title: chapter.title,
              contentLength: chapter.content.length,
              wordCount: chapter.wordCount,
              contentStart: chapter.content.substring(0, 100)
            })

            chapters.push(chapter)
            fullText += cleanText + '\n\n'
            chapterNumber++
          } else {
            console.log(`Skipping ${filename} - content too short (${cleanText.length} chars)`)
          }
        }
      } catch (error) {
        console.error(`Error processing file ${filename}:`, error)
        continue
      }
    }

    console.log(`Total chapters extracted: ${chapters.length}`)

    // If no chapters found, create a single chapter from all content
    if (chapters.length === 0) {
      chapters.push({
        title: 'Content',
        content: 'No readable content found in this EPUB file.',
        chapter_number: 1,
        word_count: 0
      })
    }

    const wordCount = wordTokenizer.tokenize(fullText)?.length || 0
    const readingStats = readingTime(fullText || 'No content')
    let readabilityScoreValue = 0
    try {
      readabilityScoreValue = fullText && typeof readabilityScore === 'function' ? readabilityScore(fullText) : 0
    } catch (error) {
      console.warn('Failed to calculate readability score:', error)
      readabilityScoreValue = 0
    }
    const suggestedTags = extractKeywords(fullText, metadata.subjects, wordTokenizer, sentenceTokenizer)

    return {
      chapters,
      wordCount,
      pageCount: Math.ceil(wordCount / 250), // Estimate pages (250 words per page)
      readingTimeMinutes: Math.ceil(readingStats.minutes),
      readabilityScore: readabilityScoreValue,
      suggestedTags,
      metadata,
      description: metadata.description || generateDescription(fullText, sentenceTokenizer),
      contentAnalysis: analyzeContentQuality(fullText, wordTokenizer, sentenceTokenizer)
    }
  } catch (error) {
    console.error('EPUB processing error:', error)
    throw new Error(`EPUB processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Parse XML string asynchronously
 */
function parseXmlAsync(xml: string): Promise<any> {
  return new Promise((resolve, reject) => {
    parseString(xml, (err, result) => {
      if (err) reject(err)
      else resolve(result)
    })
  })
}

/**
 * Extract text content from XML element
 */
function extractTextFromXmlElement(element: any): string {
  if (!element) return ''
  if (typeof element === 'string') return element
  if (Array.isArray(element) && element.length > 0) {
    const first = element[0]
    if (typeof first === 'string') return first
    if (first && typeof first === 'object' && first._) return first._
    if (first && typeof first === 'object' && first.$text) return first.$text
  }
  if (typeof element === 'object' && element._) return element._
  if (typeof element === 'object' && element.$text) return element.$text
  return ''
}

/**
 * Extract chapters from plain text using common patterns
 */
function extractChaptersFromText(text: string, wordTokenizer: any): Chapter[] {
  const chapters: Chapter[] = []
  
  // Common chapter patterns
  const chapterPatterns = [
    /^Chapter\s+(\d+|[IVXLCDM]+)[\s\-:]*(.*)$/gmi,
    /^(\d+)[\.\s]+(.*)$/gm,
    /^Part\s+(\d+|[IVXLCDM]+)[\s\-:]*(.*)$/gmi,
    /^Section\s+(\d+)[\s\-:]*(.*)$/gmi
  ]

  let bestSplit = null
  let maxChapters = 0

  // Try each pattern and use the one that finds the most chapters
  for (const pattern of chapterPatterns) {
    const matches = Array.from(text.matchAll(pattern))
    if (matches.length > maxChapters) {
      maxChapters = matches.length
      bestSplit = matches
    }
  }

  if (bestSplit && bestSplit.length > 1) {
    // Split text by chapter markers
    for (let i = 0; i < bestSplit.length; i++) {
      const match = bestSplit[i]
      const nextMatch = bestSplit[i + 1]
      
      const startIndex = match.index!
      const endIndex = nextMatch ? nextMatch.index! : text.length
      
      const chapterContent = text.slice(startIndex, endIndex).trim()
      const title = match[2]?.trim() || `Chapter ${i + 1}`
      
      if (chapterContent.length > 200) { // Only include substantial chapters
        chapters.push({
          title,
          content: chapterContent,
          chapter_number: chapters.length + 1,
          word_count: wordTokenizer.tokenize(chapterContent.toLowerCase())?.length || 0
        })
      }
    }
  } else {
    // If no clear chapter structure, create a single chapter
    chapters.push({
      title: 'Full Text',
      content: text,
      chapter_number: 1,
      word_count: wordTokenizer.tokenize(text.toLowerCase())?.length || 0
    })
  }

  return chapters
}

/**
 * Clean HTML text and extract readable content
 */
function cleanHtmlText(html: string): string {
  // Enhanced HTML processing to preserve formatting
  let text = html
    // Preserve headings
    .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n\n# $1\n\n')
    .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n\n## $1\n\n')
    .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n\n### $1\n\n')
    .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n\n#### $1\n\n')
    .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n\n##### $1\n\n')
    .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n\n###### $1\n\n')

    // Preserve emphasis
    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
    .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
    .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
    .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')

    // Preserve line breaks and paragraphs
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/p>/gi, '\n\n')
    .replace(/<\/div>/gi, '\n')

    // Preserve lists
    .replace(/<li[^>]*>(.*?)<\/li>/gi, '• $1\n')
    .replace(/<\/ul>/gi, '\n')
    .replace(/<\/ol>/gi, '\n')

    // Preserve blockquotes
    .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '\n> $1\n\n')

    // Remove remaining HTML tags but preserve content
    .replace(/<[^>]*>/g, '')

  // Decode HTML entities
  text = text
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&apos;/g, "'")
    .replace(/&mdash;/g, '—')
    .replace(/&ndash;/g, '–')
    .replace(/&hellip;/g, '…')
    .replace(/&ldquo;/g, '"')
    .replace(/&rdquo;/g, '"')
    .replace(/&lsquo;/g, "'")
    .replace(/&rsquo;/g, "'")

  // Clean up whitespace while preserving intentional formatting
  text = text
    .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space
    .replace(/\n[ \t]+/g, '\n') // Remove spaces at start of lines
    .replace(/[ \t]+\n/g, '\n') // Remove spaces at end of lines
    .replace(/\n{4,}/g, '\n\n\n') // Limit to triple line breaks max
    .trim()

  return text
}

// countWords function removed - using tokenizer directly

/**
 * Calculate readability score
 */
function calculateReadabilityScore(text: string): number {
  try {
    const sentences = sentenceTokenizer.tokenize(text)
    const words = wordTokenizer.tokenize(text)
    
    if (!sentences || !words || sentences.length === 0 || words.length === 0) {
      return 0
    }

    // Use Flesch Reading Ease score
    const score = readabilityScore({
      sentence: sentences.length,
      word: words.length,
      syllable: estimateSyllables(words)
    })

    return Math.round(score.fleschReadingEase || 0)
  } catch (error) {
    console.warn('Error calculating readability score:', error)
    return 0
  }
}

/**
 * Estimate syllables in words
 */
function estimateSyllables(words: string[]): number {
  return words.reduce((total, word) => {
    // Simple syllable estimation
    const vowels = word.match(/[aeiouy]+/gi)
    const syllableCount = vowels ? vowels.length : 1
    return total + Math.max(1, syllableCount)
  }, 0)
}

/**
 * Extract keywords and suggested tags with enhanced analysis
 */
function extractKeywords(text: string, existingSubjects?: string[], wordTokenizer?: any, sentenceTokenizer?: any): string[] {
  if (!wordTokenizer) return []
  const words = wordTokenizer.tokenize(text.toLowerCase())
  if (!words) return []

  // Enhanced stop words list
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their', 'said', 'say', 'says', 'go', 'goes', 'went', 'come', 'came', 'get', 'got', 'make', 'made', 'take', 'took', 'see', 'saw', 'know', 'knew', 'think', 'thought', 'look', 'looked', 'want', 'wanted', 'use', 'used', 'find', 'found', 'give', 'gave', 'tell', 'told', 'ask', 'asked', 'work', 'worked', 'seem', 'seemed', 'feel', 'felt', 'try', 'tried', 'leave', 'left', 'call', 'called', 'just', 'like', 'time', 'way', 'day', 'man', 'woman', 'people', 'thing', 'place', 'world', 'life', 'hand', 'part', 'child', 'eye', 'woman', 'place', 'work', 'week', 'case', 'point', 'government', 'company'
  ])

  // Genre-specific keywords that should be prioritized
  const genreKeywords = new Set([
    'love', 'romance', 'mystery', 'crime', 'detective', 'murder', 'fantasy', 'magic', 'dragon', 'wizard', 'science', 'fiction', 'space', 'alien', 'robot', 'future', 'horror', 'ghost', 'vampire', 'zombie', 'thriller', 'suspense', 'adventure', 'journey', 'quest', 'hero', 'villain', 'war', 'battle', 'soldier', 'historical', 'medieval', 'ancient', 'biography', 'memoir', 'autobiography', 'business', 'entrepreneur', 'success', 'leadership', 'psychology', 'philosophy', 'religion', 'spiritual', 'health', 'fitness', 'cooking', 'recipe', 'travel', 'guide', 'education', 'learning', 'children', 'family', 'parenting', 'relationship', 'marriage', 'friendship'
  ])

  // Count word frequency with genre weighting
  const wordCount = new Map<string, number>()
  words.forEach(word => {
    if (word.length > 3 && !stopWords.has(word) && /^[a-z]+$/.test(word)) {
      const weight = genreKeywords.has(word) ? 3 : 1 // Give genre keywords more weight
      wordCount.set(word, (wordCount.get(word) || 0) + weight)
    }
  })

  // Get most frequent words
  const sortedWords = Array.from(wordCount.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 15)
    .map(([word]) => word)

  // Smart genre detection based on content
  const detectedGenres = detectGenres(text, sortedWords)

  // Combine existing subjects, detected genres, and keywords
  const allTags = [...(existingSubjects || []), ...detectedGenres, ...sortedWords]

  // Remove duplicates and return top 10
  return Array.from(new Set(allTags)).slice(0, 10)
}

/**
 * Detect likely genres based on content analysis
 */
function detectGenres(text: string, keywords: string[]): string[] {
  const genres: string[] = []
  const lowerText = text.toLowerCase()

  // Romance indicators
  if (keywords.some(k => ['love', 'romance', 'heart', 'kiss', 'relationship', 'marriage', 'wedding'].includes(k)) ||
      /\b(love|romance|heart|kiss|relationship|marriage|wedding|passion|desire)\b/g.test(lowerText)) {
    genres.push('Romance')
  }

  // Mystery/Crime indicators
  if (keywords.some(k => ['mystery', 'crime', 'detective', 'murder', 'police', 'investigation'].includes(k)) ||
      /\b(mystery|crime|detective|murder|police|investigation|suspect|evidence|clue)\b/g.test(lowerText)) {
    genres.push('Mystery')
  }

  // Fantasy indicators
  if (keywords.some(k => ['fantasy', 'magic', 'dragon', 'wizard', 'spell', 'kingdom', 'quest'].includes(k)) ||
      /\b(magic|dragon|wizard|spell|kingdom|quest|fantasy|enchanted|mystical)\b/g.test(lowerText)) {
    genres.push('Fantasy')
  }

  // Science Fiction indicators
  if (keywords.some(k => ['science', 'fiction', 'space', 'alien', 'robot', 'future', 'technology'].includes(k)) ||
      /\b(space|alien|robot|future|technology|spacecraft|galaxy|planet|android)\b/g.test(lowerText)) {
    genres.push('Science Fiction')
  }

  // Horror indicators
  if (keywords.some(k => ['horror', 'ghost', 'vampire', 'zombie', 'demon', 'evil', 'dark'].includes(k)) ||
      /\b(horror|ghost|vampire|zombie|demon|evil|dark|terror|nightmare|haunted)\b/g.test(lowerText)) {
    genres.push('Horror')
  }

  // Thriller indicators
  if (keywords.some(k => ['thriller', 'suspense', 'danger', 'chase', 'escape', 'tension'].includes(k)) ||
      /\b(thriller|suspense|danger|chase|escape|tension|adrenaline|pursuit)\b/g.test(lowerText)) {
    genres.push('Thriller')
  }

  // Historical indicators
  if (keywords.some(k => ['historical', 'history', 'medieval', 'ancient', 'century', 'war'].includes(k)) ||
      /\b(historical|history|medieval|ancient|century|war|empire|dynasty|colonial)\b/g.test(lowerText)) {
    genres.push('Historical Fiction')
  }

  // Biography/Memoir indicators
  if (keywords.some(k => ['biography', 'memoir', 'autobiography', 'life', 'born', 'childhood'].includes(k)) ||
      /\b(biography|memoir|autobiography|born|childhood|grew up|my life|personal story)\b/g.test(lowerText)) {
    genres.push('Biography')
  }

  // Business indicators
  if (keywords.some(k => ['business', 'entrepreneur', 'success', 'leadership', 'management', 'strategy'].includes(k)) ||
      /\b(business|entrepreneur|success|leadership|management|strategy|corporate|profit)\b/g.test(lowerText)) {
    genres.push('Business')
  }

  // Self-Help indicators
  if (keywords.some(k => ['self', 'help', 'improvement', 'motivation', 'success', 'guide'].includes(k)) ||
      /\b(self-help|improvement|motivation|guide|how to|steps to|achieve|personal development)\b/g.test(lowerText)) {
    genres.push('Self-Help')
  }

  return genres
}

/**
 * Generate a compelling description from the text
 */
function generateDescription(text: string, sentenceTokenizer?: any): string {
  if (!sentenceTokenizer) return ''
  const sentences = sentenceTokenizer.tokenize(text)
  if (!sentences || sentences.length === 0) return ''

  // Look for compelling opening sentences (avoid common chapter headers)
  const filteredSentences = sentences.filter(sentence => {
    const lower = sentence.toLowerCase().trim()
    return lower.length > 20 &&
           !lower.startsWith('chapter') &&
           !lower.startsWith('part') &&
           !lower.startsWith('section') &&
           !lower.match(/^(table of contents|acknowledgments|dedication|preface|introduction)/)
  })

  // Take first few compelling sentences, up to 300 characters
  let description = ''
  for (const sentence of filteredSentences.slice(0, 4)) {
    if (description.length + sentence.length > 280) break
    description += sentence.trim() + ' '
  }

  // If description is too short, add more content
  if (description.length < 100 && filteredSentences.length > 4) {
    for (const sentence of filteredSentences.slice(4, 8)) {
      if (description.length + sentence.length > 280) break
      description += sentence.trim() + ' '
    }
  }

  return description.trim()
}

/**
 * Analyze content quality and provide insights
 */
export function analyzeContentQuality(text: string, wordTokenizer?: any, sentenceTokenizer?: any): {
  complexity: 'Simple' | 'Moderate' | 'Complex'
  sentiment: 'Positive' | 'Neutral' | 'Negative'
  pacing: 'Fast' | 'Moderate' | 'Slow'
  dialogueRatio: number
  descriptiveRatio: number
  actionRatio: number
} {
  if (!sentenceTokenizer || !wordTokenizer) {
    return {
      complexity: 'Moderate',
      sentiment: 'Neutral',
      pacing: 'Moderate',
      dialogueRatio: 0,
      descriptiveRatio: 0,
      actionRatio: 0
    }
  }

  const sentences = sentenceTokenizer.tokenize(text)
  const words = wordTokenizer.tokenize(text.toLowerCase())

  if (!sentences || !words) {
    return {
      complexity: 'Moderate',
      sentiment: 'Neutral',
      pacing: 'Moderate',
      dialogueRatio: 0,
      descriptiveRatio: 0,
      actionRatio: 0
    }
  }

  // Analyze sentence complexity
  const avgWordsPerSentence = words.length / sentences.length
  const complexity = avgWordsPerSentence > 20 ? 'Complex' : avgWordsPerSentence > 12 ? 'Moderate' : 'Simple'

  // Basic sentiment analysis
  const positiveWords = ['good', 'great', 'excellent', 'wonderful', 'amazing', 'beautiful', 'love', 'happy', 'joy', 'success', 'win', 'victory', 'hope', 'bright', 'smile', 'laugh']
  const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'hate', 'sad', 'angry', 'fear', 'death', 'pain', 'suffer', 'lose', 'fail', 'dark', 'cry', 'scream']

  const positiveCount = words.filter(word => positiveWords.includes(word)).length
  const negativeCount = words.filter(word => negativeWords.includes(word)).length

  const sentiment = positiveCount > negativeCount * 1.5 ? 'Positive' :
                   negativeCount > positiveCount * 1.5 ? 'Negative' : 'Neutral'

  // Analyze pacing through sentence length variation
  const shortSentences = sentences.filter(s => s.split(' ').length < 8).length
  const longSentences = sentences.filter(s => s.split(' ').length > 20).length
  const shortRatio = shortSentences / sentences.length

  const pacing = shortRatio > 0.4 ? 'Fast' : shortRatio < 0.2 ? 'Slow' : 'Moderate'

  // Analyze content types
  const dialogueMarkers = text.match(/["']/g)?.length || 0
  const dialogueRatio = Math.min(dialogueMarkers / (words.length / 100), 100) // Normalize to percentage

  const descriptiveWords = ['beautiful', 'dark', 'bright', 'large', 'small', 'old', 'new', 'red', 'blue', 'green', 'tall', 'short', 'wide', 'narrow', 'deep', 'shallow', 'rough', 'smooth', 'soft', 'hard']
  const descriptiveCount = words.filter(word => descriptiveWords.includes(word)).length
  const descriptiveRatio = (descriptiveCount / words.length) * 100

  const actionWords = ['run', 'jump', 'fight', 'chase', 'escape', 'attack', 'defend', 'move', 'rush', 'dash', 'leap', 'strike', 'hit', 'kick', 'punch', 'shoot', 'throw', 'catch']
  const actionCount = words.filter(word => actionWords.includes(word)).length
  const actionRatio = (actionCount / words.length) * 100

  return {
    complexity,
    sentiment,
    pacing,
    dialogueRatio: Math.round(dialogueRatio),
    descriptiveRatio: Math.round(descriptiveRatio),
    actionRatio: Math.round(actionRatio)
  }
}
