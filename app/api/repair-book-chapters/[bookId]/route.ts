import { createSupabaseClient } from "@/lib/supabase/server"
import { NextRequest, NextResponse } from "next/server"
import { processEbook } from "@/lib/ebook-processor"

export async function POST(
  request: NextRequest,
  { params }: { params: { bookId: string } }
) {
  try {
    const supabase = createSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get book details and verify ownership
    const { data: book, error: bookError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        user_id,
        is_ebook,
        ebook_file_url,
        ebook_file_type,
        meta_description
      `)
      .eq('id', params.bookId)
      .eq('user_id', user.id)
      .single()

    if (bookError || !book) {
      return NextResponse.json({ error: 'Book not found or access denied' }, { status: 404 })
    }

    if (!book.is_ebook || !book.ebook_file_url) {
      return NextResponse.json({ error: 'This is not an ebook or no file URL found' }, { status: 400 })
    }

    console.log(`Repairing chapters for book: ${book.title}`)
    console.log(`File URL: ${book.ebook_file_url}`)
    console.log(`File Type: ${book.ebook_file_type}`)

    // Check current chapters
    const { data: existingChapters } = await supabase
      .from('chapters')
      .select('id, title, content, chapter_number')
      .eq('project_id', params.bookId)
      .order('chapter_number')

    console.log(`Found ${existingChapters?.length || 0} existing chapters`)

    // Check if chapters have the book description as content (the bug)
    const hasDescriptionBug = existingChapters?.some(chapter => 
      chapter.content === book.meta_description
    )

    if (hasDescriptionBug) {
      console.log('Detected description bug - chapters contain book description instead of content')
    }

    // Re-process the ebook file
    try {
      const result = await processEbook(book.ebook_file_url, book.ebook_file_type as 'pdf' | 'epub')
      
      console.log(`Extracted ${result.chapters.length} chapters from file`)
      
      if (result.chapters.length === 0) {
        return NextResponse.json({ 
          error: 'No chapters could be extracted from the file',
          debug: {
            fileUrl: book.ebook_file_url,
            fileType: book.ebook_file_type,
            processingResult: result
          }
        }, { status: 400 })
      }

      // Clear existing chapters
      console.log('Clearing existing chapters...')
      await supabase
        .from('chapters')
        .delete()
        .eq('project_id', params.bookId)

      // Create new chapters with proper content
      console.log(`Creating ${result.chapters.length} new chapters...`)
      
      const chaptersToInsert = result.chapters.map((chapter, index) => ({
        project_id: params.bookId,
        user_id: user.id,
        title: chapter.title,
        content: chapter.content,
        chapter_number: index + 1,
        word_count: chapter.wordCount || chapter.word_count,
        is_published: true
      }))

      const { error: chaptersError, data: insertedChapters } = await supabase
        .from('chapters')
        .insert(chaptersToInsert)
        .select()

      if (chaptersError) {
        console.error('Error creating chapters:', chaptersError)
        throw new Error(`Failed to create chapters: ${chaptersError.message}`)
      }

      // Update book stats
      const { error: updateError } = await supabase
        .from('projects')
        .update({
          total_chapters: result.chapters.length,
          total_words: result.wordCount,
          reading_time_minutes: result.readingTimeMinutes,
          is_complete: true
        })
        .eq('id', params.bookId)

      if (updateError) {
        console.error('Error updating book stats:', updateError)
      }

      // Verify the repair worked
      const { data: newChapters } = await supabase
        .from('chapters')
        .select('id, title, content, chapter_number, word_count')
        .eq('project_id', params.bookId)
        .order('chapter_number')

      const repairSummary = {
        before: {
          chapters: existingChapters?.length || 0,
          hadDescriptionBug: hasDescriptionBug,
          firstChapterContentPreview: existingChapters?.[0]?.content?.substring(0, 100) || 'No content'
        },
        after: {
          chapters: newChapters?.length || 0,
          firstChapterContentPreview: newChapters?.[0]?.content?.substring(0, 100) || 'No content',
          totalWords: result.wordCount,
          readingTime: result.readingTimeMinutes
        }
      }

      console.log('Repair completed successfully:', repairSummary)

      return NextResponse.json({
        success: true,
        message: 'Book chapters repaired successfully',
        summary: repairSummary,
        chaptersCreated: result.chapters.length
      })

    } catch (processingError) {
      console.error('Error processing ebook:', processingError)
      return NextResponse.json({
        error: 'Failed to process ebook file',
        details: processingError.message,
        debug: {
          fileUrl: book.ebook_file_url,
          fileType: book.ebook_file_type
        }
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Repair API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
