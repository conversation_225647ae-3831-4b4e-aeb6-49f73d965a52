import { NextRequest, NextResponse } from 'next/server'
import { processEbookBuffer } from '@/lib/ebook-processor'

export async function POST(request: NextRequest) {
  console.log('Preview API endpoint called')

  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      console.error('No file provided in request')
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    console.log('File received:', {
      name: file.name,
      size: file.size,
      type: file.type
    })

    // Check file type
    const fileType = file.name.toLowerCase().endsWith('.epub') ? 'epub' : 'pdf'

    if (!['epub', 'pdf'].includes(fileType)) {
      console.error('Invalid file type:', file.name)
      return NextResponse.json(
        { error: 'File must be EPUB or PDF' },
        { status: 400 }
      )
    }

    console.log(`Processing ${fileType.toUpperCase()} file for preview:`, file.name)

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    console.log('Buffer created, size:', buffer.length)

    // Process the ebook
    const result = await processEbookBuffer(buffer, fileType as 'pdf' | 'epub')
    
    console.log('Preview processing completed:', {
      chapters: result.chapters.length,
      wordCount: result.wordCount,
      pageCount: result.pageCount
    })
    
    if (result.chapters.length === 0) {
      return NextResponse.json(
        { error: 'No chapters could be extracted from this file' },
        { status: 400 }
      )
    }

    // Return the processed chapters
    return NextResponse.json({
      success: true,
      chapters: result.chapters.map((chapter, index) => ({
        id: `preview-${index}`,
        title: chapter.title,
        content: chapter.content,
        chapter_number: index + 1,
        word_count: chapter.wordCount || chapter.word_count || 0
      })),
      metadata: {
        totalChapters: result.chapters.length,
        totalWords: result.wordCount,
        readingTime: result.readingTimeMinutes,
        pageCount: result.pageCount
      }
    })

  } catch (error) {
    console.error('Preview processing error:', error)

    // Ensure we always return JSON, even for unexpected errors
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'

    return NextResponse.json(
      {
        success: false,
        error: `Failed to process file: ${errorMessage}`,
        details: error instanceof Error ? error.stack : String(error)
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}

// Helper function to process buffer directly
async function processEbookBuffer(buffer: Buffer, fileType: 'pdf' | 'epub') {
  // We need to modify the ebook processor to accept a buffer directly
  // For now, let's create a modified version that works with buffers

  if (fileType === 'epub') {
    return await processEPUBBuffer(buffer)
  } else {
    return await processPDFBuffer(buffer)
  }
}

// EPUB processing function that works with buffers
async function processEPUBBuffer(buffer: Buffer) {
  try {
    // Dynamic imports to avoid build-time issues
    const JSZip = (await import('jszip')).default
    const { parseString } = await import('xml2js')
    const { WordTokenizer, SentenceTokenizer } = await import('natural')

    const wordTokenizer = new WordTokenizer()
    const sentenceTokenizer = new SentenceTokenizer(['Dr.', 'Mr.', 'Mrs.', 'Ms.', 'Prof.', 'Sr.', 'Jr.'])

    // Local XML parsing function
    const parseXmlAsync = (xml: string): Promise<any> => {
      return new Promise((resolve, reject) => {
        parseString(xml, (err, result) => {
          if (err) reject(err)
          else resolve(result)
        })
      })
    }

    // Parse EPUB as ZIP file
    const zip = await JSZip.loadAsync(buffer)

    const chapters: any[] = []
    let fullText = ''
    let chapterNumber = 1

    // Find HTML/XHTML files in the EPUB
    const htmlFiles = Object.keys(zip.files).filter(filename =>
      filename.match(/\.(x?html?)$/i) && !filename.startsWith('META-INF/')
    )

    console.log(`Found ${htmlFiles.length} HTML files in EPUB:`, htmlFiles)

    // Sort files properly by chapter number
    const sortedHtmlFiles = htmlFiles.sort((a, b) => {
      const aMatch = a.match(/chapter-(\d+)/i)
      const bMatch = b.match(/chapter-(\d+)/i)

      if (aMatch && bMatch) {
        return parseInt(aMatch[1]) - parseInt(bMatch[1])
      }

      // Put non-chapter files at the end
      if (aMatch && !bMatch) return -1
      if (!aMatch && bMatch) return 1

      return a.localeCompare(b)
    })

    console.log('Sorted HTML files:', sortedHtmlFiles)

    // For preview, only process the first chapter
    const chapterFiles = sortedHtmlFiles.filter(filename =>
      filename.match(/chapter-0*1\.(x?html?)$/i) // Only chapter 1
    )

    console.log('Processing chapter 1 for preview:', chapterFiles)

    // Process only Chapter 1 for preview
    for (const filename of chapterFiles) {
      try {
        const file = zip.file(filename)
        if (file) {
          const htmlContent = await file.async('text')
          const cleanText = cleanHtmlText(htmlContent)

          if (cleanText.trim().length > 100) { // Only include substantial content
            // Try to extract title from HTML or use filename
            const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i)
            const h1Match = htmlContent.match(/<h1[^>]*>([^<]+)<\/h1>/i)
            const h2Match = htmlContent.match(/<h2[^>]*>([^<]+)<\/h2>/i)
            const chapterTitle = titleMatch?.[1] || h1Match?.[1] || h2Match?.[1] || `Chapter ${chapterNumber}`

            const wordCount = wordTokenizer.tokenize(cleanText)?.length || 0

            const chapter = {
              title: chapterTitle.trim(),
              content: cleanText,
              wordCount: wordCount
            }

            chapters.push(chapter)
            fullText += cleanText + '\n\n'
            chapterNumber++
          }
        }
      } catch (error) {
        console.error(`Error processing file ${filename}:`, error)
        continue
      }
    }

    const totalWordCount = wordTokenizer.tokenize(fullText)?.length || 0

    return {
      chapters,
      wordCount: totalWordCount,
      pageCount: Math.ceil(totalWordCount / 250), // Estimate pages (250 words per page)
      readingTimeMinutes: Math.ceil(totalWordCount / 200), // Estimate reading time
    }
  } catch (error) {
    console.error('EPUB processing error:', error)
    throw new Error(`EPUB processing failed: ${error.message}`)
  }
}

// PDF processing function (simplified for preview)
async function processPDFBuffer(buffer: Buffer) {
  // For now, return a simple response for PDF
  // You can implement PDF processing later if needed
  return {
    chapters: [{
      title: 'PDF Content',
      content: 'PDF preview is not yet implemented. Please use EPUB files for preview.',
      wordCount: 0
    }],
    wordCount: 0,
    pageCount: 1,
    readingTimeMinutes: 1
  }
}

// Enhanced HTML processing to preserve formatting
function cleanHtmlText(html: string): string {
  // First, preserve important formatting by converting to markdown-like syntax
  let text = html
    // Preserve headings
    .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n\n# $1\n\n')
    .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n\n## $1\n\n')
    .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n\n### $1\n\n')
    .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n\n#### $1\n\n')
    .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n\n##### $1\n\n')
    .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n\n###### $1\n\n')

    // Preserve emphasis
    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
    .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
    .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
    .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')

    // Preserve line breaks and paragraphs
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/p>/gi, '\n\n')
    .replace(/<\/div>/gi, '\n')

    // Preserve lists
    .replace(/<li[^>]*>(.*?)<\/li>/gi, '• $1\n')
    .replace(/<\/ul>/gi, '\n')
    .replace(/<\/ol>/gi, '\n')

    // Preserve blockquotes
    .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '\n> $1\n\n')

    // Remove remaining HTML tags but preserve content
    .replace(/<[^>]*>/g, '')

  // Decode HTML entities
  text = text
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&apos;/g, "'")
    .replace(/&mdash;/g, '—')
    .replace(/&ndash;/g, '–')
    .replace(/&hellip;/g, '…')
    .replace(/&ldquo;/g, '"')
    .replace(/&rdquo;/g, '"')
    .replace(/&lsquo;/g, ''')
    .replace(/&rsquo;/g, ''')

  // Clean up whitespace while preserving intentional formatting
  text = text
    .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space
    .replace(/\n[ \t]+/g, '\n') // Remove spaces at start of lines
    .replace(/[ \t]+\n/g, '\n') // Remove spaces at end of lines
    .replace(/\n{4,}/g, '\n\n\n') // Limit to triple line breaks max
    .trim()

  return text
}
