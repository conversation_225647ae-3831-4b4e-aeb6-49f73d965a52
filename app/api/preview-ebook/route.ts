import { NextRequest, NextResponse } from 'next/server'
import { processEbook } from '@/lib/ebook-processor'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Check file type
    const fileType = file.name.toLowerCase().endsWith('.epub') ? 'epub' : 'pdf'
    
    if (!['epub', 'pdf'].includes(fileType)) {
      return NextResponse.json(
        { error: 'File must be EPUB or PDF' },
        { status: 400 }
      )
    }

    console.log(`Processing ${fileType.toUpperCase()} file for preview:`, file.name)
    
    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    
    // Process the ebook
    const result = await processEbook(buffer, fileType as 'pdf' | 'epub')
    
    console.log('Preview processing completed:', {
      chapters: result.chapters.length,
      wordCount: result.wordCount,
      pageCount: result.pageCount
    })
    
    if (result.chapters.length === 0) {
      return NextResponse.json(
        { error: 'No chapters could be extracted from this file' },
        { status: 400 }
      )
    }

    // Return the processed chapters
    return NextResponse.json({
      success: true,
      chapters: result.chapters.map((chapter, index) => ({
        id: `preview-${index}`,
        title: chapter.title,
        content: chapter.content,
        chapter_number: index + 1,
        word_count: chapter.wordCount || chapter.word_count || 0
      })),
      metadata: {
        totalChapters: result.chapters.length,
        totalWords: result.wordCount,
        readingTime: result.readingTimeMinutes,
        pageCount: result.pageCount
      }
    })

  } catch (error) {
    console.error('Preview processing error:', error)
    return NextResponse.json(
      { error: `Failed to process file: ${error.message}` },
      { status: 500 }
    )
  }
}

// Helper function to process buffer directly
async function processEbook(buffer: Buffer, fileType: 'pdf' | 'epub') {
  // We need to modify the ebook processor to accept a buffer directly
  // For now, let's create a modified version that works with buffers

  if (fileType === 'epub') {
    return await processEPUBBuffer(buffer)
  } else {
    return await processPDFBuffer(buffer)
  }
}

// EPUB processing function that works with buffers
async function processEPUBBuffer(buffer: Buffer) {
  try {
    // Dynamic imports to avoid build-time issues
    const JSZip = (await import('jszip')).default
    const { parseString } = await import('xml2js')
    const { WordTokenizer, SentenceTokenizer } = await import('natural')

    const wordTokenizer = new WordTokenizer()
    const sentenceTokenizer = new SentenceTokenizer(['Dr.', 'Mr.', 'Mrs.', 'Ms.', 'Prof.', 'Sr.', 'Jr.'])

    // Local XML parsing function
    const parseXmlAsync = (xml: string): Promise<any> => {
      return new Promise((resolve, reject) => {
        parseString(xml, (err, result) => {
          if (err) reject(err)
          else resolve(result)
        })
      })
    }

    // Parse EPUB as ZIP file
    const zip = await JSZip.loadAsync(buffer)

    const chapters: any[] = []
    let fullText = ''
    let chapterNumber = 1

    // Find HTML/XHTML files in the EPUB
    const htmlFiles = Object.keys(zip.files).filter(filename =>
      filename.match(/\.(x?html?)$/i) && !filename.startsWith('META-INF/')
    )

    console.log(`Found ${htmlFiles.length} HTML files in EPUB:`, htmlFiles)

    // Process each HTML file as a potential chapter
    for (const filename of htmlFiles) {
      try {
        const file = zip.file(filename)
        if (file) {
          const htmlContent = await file.async('text')
          const cleanText = cleanHtmlText(htmlContent)

          if (cleanText.trim().length > 100) { // Only include substantial content
            // Try to extract title from HTML or use filename
            const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i)
            const h1Match = htmlContent.match(/<h1[^>]*>([^<]+)<\/h1>/i)
            const h2Match = htmlContent.match(/<h2[^>]*>([^<]+)<\/h2>/i)
            const chapterTitle = titleMatch?.[1] || h1Match?.[1] || h2Match?.[1] || `Chapter ${chapterNumber}`

            const wordCount = wordTokenizer.tokenize(cleanText)?.length || 0

            const chapter = {
              title: chapterTitle.trim(),
              content: cleanText,
              wordCount: wordCount
            }

            chapters.push(chapter)
            fullText += cleanText + '\n\n'
            chapterNumber++
          }
        }
      } catch (error) {
        console.error(`Error processing file ${filename}:`, error)
        continue
      }
    }

    const totalWordCount = wordTokenizer.tokenize(fullText)?.length || 0

    return {
      chapters,
      wordCount: totalWordCount,
      pageCount: Math.ceil(totalWordCount / 250), // Estimate pages (250 words per page)
      readingTimeMinutes: Math.ceil(totalWordCount / 200), // Estimate reading time
    }
  } catch (error) {
    console.error('EPUB processing error:', error)
    throw new Error(`EPUB processing failed: ${error.message}`)
  }
}

// PDF processing function (simplified for preview)
async function processPDFBuffer(buffer: Buffer) {
  // For now, return a simple response for PDF
  // You can implement PDF processing later if needed
  return {
    chapters: [{
      title: 'PDF Content',
      content: 'PDF preview is not yet implemented. Please use EPUB files for preview.',
      wordCount: 0
    }],
    wordCount: 0,
    pageCount: 1,
    readingTimeMinutes: 1
  }
}

// Clean HTML text function
function cleanHtmlText(html: string): string {
  // Convert paragraph and div tags to line breaks
  let text = html
    .replace(/<\/p>/gi, '\n\n')
    .replace(/<\/div>/gi, '\n')
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/h[1-6]>/gi, '\n\n')

  // Remove all other HTML tags
  text = text.replace(/<[^>]*>/g, ' ')

  // Decode HTML entities
  text = text
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&apos;/g, "'")
    .replace(/&mdash;/g, '—')
    .replace(/&ndash;/g, '–')
    .replace(/&hellip;/g, '…')

  // Clean up whitespace while preserving paragraph breaks
  text = text
    .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space
    .replace(/\n[ \t]+/g, '\n') // Remove spaces at start of lines
    .replace(/[ \t]+\n/g, '\n') // Remove spaces at end of lines
    .replace(/\n{3,}/g, '\n\n') // Multiple line breaks to double
    .trim()

  return text
}
